using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace TraceLens.ThemeControl.Controls.HIcon
{
    /// <summary>
    /// HIcon - 基于 FontAwesome 4.7.0 的图标控件
    /// 支持通过英文名称直接使用 FontAwesome 图标
    /// </summary>
    public class HIcon : ContentControl
    {
        #region 依赖属性

        /// <summary>
        /// 图标名称（FontAwesome 英文名，不需要 fa- 前缀）
        /// </summary>
        public static readonly DependencyProperty IconNameProperty =
            DependencyProperty.Register(nameof(IconName), typeof(string), typeof(HIcon),
                new PropertyMetadata(string.Empty, OnIconNameChanged));

        /// <summary>
        /// 图标大小
        /// </summary>
        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(nameof(IconSize), typeof(double), typeof(HIcon),
                new PropertyMetadata(16.0, OnIconSizeChanged, CoerceIconSize));

        /// <summary>
        /// 图标颜色
        /// </summary>
        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register(nameof(IconColor), typeof(Brush), typeof(HIcon),
                new PropertyMetadata(Brushes.Black));

        #endregion

        #region 属性

        /// <summary>
        /// 图标名称（FontAwesome 英文名，不需要 fa- 前缀）
        /// 例如：home, user, settings, search 等
        /// </summary>
        public string IconName
        {
            get => (string)GetValue(IconNameProperty);
            set => SetValue(IconNameProperty, value);
        }

        /// <summary>
        /// 图标大小
        /// </summary>
        public double IconSize
        {
            get => (double)GetValue(IconSizeProperty);
            set => SetValue(IconSizeProperty, value);
        }

        /// <summary>
        /// 图标颜色
        /// </summary>
        public Brush IconColor
        {
            get => (Brush)GetValue(IconColorProperty);
            set => SetValue(IconColorProperty, value);
        }

        #endregion

        #region 构造函数

        static HIcon()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HIcon), new FrameworkPropertyMetadata(typeof(HIcon)));
        }

        public HIcon()
        {
            // 初始化时更新图标
            UpdateIcon();
        }

        #endregion

        #region 私有方法

        private static void OnIconNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private static void OnIconSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private static object CoerceIconSize(DependencyObject d, object value)
        {
            if (value is double size)
            {
                // 确保图标大小在合理范围内，最小1，最大1000
                return Math.Max(1.0, Math.Min(1000.0, size));
            }
            // 如果值无效，返回默认值
            return 16.0;
        }

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            UpdateIcon();
        }

        private void UpdateIcon()
        {
            if (string.IsNullOrEmpty(IconName))
            {
                return;
            }

            // 获取图标的 Unicode 字符
            var unicode = GetIconUnicode(IconName);
            if (!string.IsNullOrEmpty(unicode))
            {
                // 通过模板更新显示
                if (GetTemplateChild("PART_IconText") is TextBlock iconText)
                {
                    iconText.Text = unicode;
                }
            }
        }

        /// <summary>
        /// 根据图标名称获取对应的 Unicode 字符
        /// </summary>
        /// <param name="iconName">图标名称（不含 fa- 前缀）</param>
        /// <returns>Unicode 字符</returns>
        private string? GetIconUnicode(string iconName)
        {
            // 移除可能的 fa- 前缀
            if (iconName.StartsWith("fa-"))
            {
                iconName = iconName.Substring(3);
            }

            // FontAwesome 4.7.0 图标映射表（部分常用图标）
            var iconMap = GetIconMap();
            
            return iconMap.TryGetValue(iconName, out var unicode) ? unicode : null;
        }

        /// <summary>
        /// 获取 FontAwesome 4.7.0 图标映射表
        /// </summary>
        /// <returns>图标名称到 Unicode 的映射</returns>
        private Dictionary<string, string> GetIconMap()
        {
            return FontAwesome4IconMap.GetIconMap();
        }

        #endregion
    }
}
