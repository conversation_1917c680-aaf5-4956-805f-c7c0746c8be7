<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Fonts.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 窗口样式 -->
    <Style x:Key="IndustrialWindow" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource IndustrialFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBase}"/>
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="ResizeMode" Value="CanResize"/>
    </Style>

    
    <Style x:Key="IndustrialTextBlock" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource IndustrialFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBase}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightRegular}"/>
    </Style>


    <!-- 补充文本样式 -->
    <Style x:Key="SupplementaryText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeExtraSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCompact}"/>
    </Style>

    <!-- 正文（小）样式 -->
    <Style x:Key="BodySmallText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource TextRegularBrush}"/>
    </Style>

    <!-- 正文样式 -->
    <Style x:Key="BodyText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeBase}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>

    <!-- 小标题样式 -->
    <Style x:Key="SubtitleText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>

    <!-- 标题样式 -->
    <Style x:Key="TitleText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>

    <!-- 主标题样式 -->
    <Style x:Key="MainTitleText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeExtraLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightBold}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>

    <!-- 图标文本样式 -->
    <Style x:Key="IconText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource FontAwesome}"/>
        <Setter Property="FontSize" Value="{StaticResource IconSizeMedium}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

</ResourceDictionary>
