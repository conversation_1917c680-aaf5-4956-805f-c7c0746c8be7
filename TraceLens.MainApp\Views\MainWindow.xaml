<Window x:Class="TraceLens.MainApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:local="clr-namespace:TraceLens.MainApp.Views"
        xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls.HIcon;assembly=TraceLens.ThemeControl"

        prism:ViewModelLocator.AutoWireViewModel="True"
        mc:Ignorable="d"
        Title="TraceLens 控件演示" Height="600" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="TraceLens HIcon 控件演示"
                   FontSize="24"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="20"/>

        <!-- 内容区域 -->
        <StackPanel Grid.Row="1" Margin="20">

            <!-- HIcon 控件演示 -->
            <TextBlock Text="TraceLens HIcon 控件演示" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,10"/>
            <TextBlock Text="应用程序启动成功！Element UI 主题已加载。" FontSize="14" Foreground="Green" Margin="0,0,0,20"/>

            <!-- 基础图标测试 -->
            <TextBlock Text="基础图标:" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <hicon:HIcon IconName="home" Margin="5" ToolTip="home"/>
                <hicon:HIcon IconName="user" Margin="5" ToolTip="user"/>
                <hicon:HIcon IconName="search" Margin="5" ToolTip="search"/>
                <hicon:HIcon IconName="cog" Margin="5" ToolTip="cog"/>
                <hicon:HIcon IconName="heart" Margin="5" ToolTip="heart"/>
            </StackPanel>

        </StackPanel>
    </Grid>
</Window>
