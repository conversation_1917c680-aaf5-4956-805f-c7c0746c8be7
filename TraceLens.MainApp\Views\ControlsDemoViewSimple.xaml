﻿<UserControl x:Class="TraceLens.MainApp.Views.ControlsDemoViewSimple"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls.HIcon;assembly=TraceLens.ThemeControl"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            
            <!-- 页面标题 -->
            <TextBlock Text="TraceLens 控件库演示" 
                       FontSize="24" 
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,30"/>
            
            <!-- HIcon 控件演示区域 -->
            <Border Background="White" 
                    BorderBrush="LightGray" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20" 
                    Margin="0,0,0,20">
                
                <StackPanel>
                    <TextBlock Text="HIcon 图标控件" 
                               FontSize="18" 
                               FontWeight="SemiBold"
                               Margin="0,0,0,15"/>
                    
                    <!-- 交互式演示区域 -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 控制面板 -->
                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="控制面板" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                            
                            <!-- 图标选择 -->
                            <TextBlock Text="选择图标:" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding CommonIcons}" 
                                      SelectedItem="{Binding SelectedIconName}"
                                      Margin="0,0,0,15"/>
                            
                            <!-- 大小选择 -->
                            <TextBlock Text="图标大小:" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding IconSizes}" 
                                      SelectedItem="{Binding SelectedIconSize}"
                                      Margin="0,0,0,15"/>
                            
                            <!-- 当前设置显示 -->
                            <TextBlock Margin="0,0,0,5">
                                <Run Text="当前图标: "/>
                                <Run Text="{Binding SelectedIconName}" FontWeight="Bold"/>
                            </TextBlock>
                            <TextBlock>
                                <Run Text="当前大小: "/>
                                <Run Text="{Binding SelectedIconSize}" FontWeight="Bold"/>
                                <Run Text="px"/>
                            </TextBlock>
                        </StackPanel>
                        
                        <!-- 预览区域 -->
                        <Border Grid.Column="1" 
                                Background="#F5F7FA"
                                BorderBrush="#E4E7ED"
                                BorderThickness="1"
                                CornerRadius="4"
                                MinHeight="150">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="预览效果" 
                                           HorizontalAlignment="Center" Margin="0,0,0,20"/>
                                
                                <!-- 动态图标预览 -->
                                <hicon:HIcon IconName="{Binding SelectedIconName}" 
                                             IconSize="{Binding SelectedIconSize}"
                                             IconColor="#409EFF"
                                             HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                    
                    <!-- 预定义样式展示 -->
                    <TextBlock Text="预定义样式" FontSize="16" FontWeight="Medium" Margin="0,20,0,10"/>
                    <WrapPanel Margin="0,0,0,20">
                        <!-- 不同颜色样式 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,20,10">
                            <TextBlock Text="颜色样式: " VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <hicon:HIcon IconName="check-circle" IconColor="#67C23A" Margin="5"/>
                            <hicon:HIcon IconName="exclamation-triangle" IconColor="#E6A23C" Margin="5"/>
                            <hicon:HIcon IconName="times-circle" IconColor="#F56C6C" Margin="5"/>
                            <hicon:HIcon IconName="info-circle" IconColor="#909399" Margin="5"/>
                            <hicon:HIcon IconName="cog" IconColor="#409EFF" Margin="5"/>
                        </StackPanel>
                        
                        <!-- 不同大小样式 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,20,10">
                            <TextBlock Text="大小样式: " VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <hicon:HIcon IconName="star" IconSize="14" Margin="5"/>
                            <hicon:HIcon IconName="star" IconSize="16" Margin="5"/>
                            <hicon:HIcon IconName="star" IconSize="20" Margin="5"/>
                            <hicon:HIcon IconName="star" IconSize="24" Margin="5"/>
                        </StackPanel>
                    </WrapPanel>
                    
                    <!-- 常用图标展示 -->
                    <TextBlock Text="常用图标展示" FontSize="16" FontWeight="Medium" Margin="0,20,0,10"/>
                    <WrapPanel>
                        <!-- 导航图标 -->
                        <Border Background="#FAFAFA" 
                                BorderBrush="#EBEEF5"
                                BorderThickness="1" 
                                CornerRadius="4" 
                                Padding="10" 
                                Margin="0,0,10,10">
                            <StackPanel>
                                <TextBlock Text="导航" 
                                           HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <hicon:HIcon IconName="arrow-left" Margin="2"/>
                                    <hicon:HIcon IconName="arrow-right" Margin="2"/>
                                    <hicon:HIcon IconName="arrow-up" Margin="2"/>
                                    <hicon:HIcon IconName="arrow-down" Margin="2"/>
                                    <hicon:HIcon IconName="home" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        
                        <!-- 文件操作 -->
                        <Border Background="#FAFAFA" 
                                BorderBrush="#EBEEF5"
                                BorderThickness="1" 
                                CornerRadius="4" 
                                Padding="10" 
                                Margin="0,0,10,10">
                            <StackPanel>
                                <TextBlock Text="文件" 
                                           HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <hicon:HIcon IconName="file" Margin="2"/>
                                    <hicon:HIcon IconName="folder" Margin="2"/>
                                    <hicon:HIcon IconName="save" Margin="2"/>
                                    <hicon:HIcon IconName="download" Margin="2"/>
                                    <hicon:HIcon IconName="upload" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        
                        <!-- 编辑操作 -->
                        <Border Background="#FAFAFA" 
                                BorderBrush="#EBEEF5"
                                BorderThickness="1" 
                                CornerRadius="4" 
                                Padding="10" 
                                Margin="0,0,10,10">
                            <StackPanel>
                                <TextBlock Text="编辑" 
                                           HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <hicon:HIcon IconName="edit" Margin="2"/>
                                    <hicon:HIcon IconName="trash" Margin="2"/>
                                    <hicon:HIcon IconName="copy" Margin="2"/>
                                    <hicon:HIcon IconName="cut" Margin="2"/>
                                    <hicon:HIcon IconName="paste" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        
                        <!-- 媒体控制 -->
                        <Border Background="#FAFAFA" 
                                BorderBrush="#EBEEF5"
                                BorderThickness="1" 
                                CornerRadius="4" 
                                Padding="10" 
                                Margin="0,0,10,10">
                            <StackPanel>
                                <TextBlock Text="媒体" 
                                           HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <hicon:HIcon IconName="play" Margin="2"/>
                                    <hicon:HIcon IconName="pause" Margin="2"/>
                                    <hicon:HIcon IconName="stop" Margin="2"/>
                                    <hicon:HIcon IconName="forward" Margin="2"/>
                                    <hicon:HIcon IconName="backward" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        
                        <!-- 状态图标 -->
                        <Border Background="#FAFAFA" 
                                BorderBrush="#EBEEF5"
                                BorderThickness="1" 
                                CornerRadius="4" 
                                Padding="10" 
                                Margin="0,0,10,10">
                            <StackPanel>
                                <TextBlock Text="状态" 
                                           HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <hicon:HIcon IconName="check" IconColor="#67C23A" Margin="2"/>
                                    <hicon:HIcon IconName="warning" IconColor="#E6A23C" Margin="2"/>
                                    <hicon:HIcon IconName="times" IconColor="#F56C6C" Margin="2"/>
                                    <hicon:HIcon IconName="info" IconColor="#909399" Margin="2"/>
                                    <hicon:HIcon IconName="question" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </WrapPanel>
                    
                    <!-- 使用说明 -->
                    <TextBlock Text="使用说明" FontSize="16" FontWeight="Medium" Margin="0,30,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        <Run Text="HIcon 控件让您可以轻松使用 FontAwesome 4.7.0 图标。只需指定图标名称即可："/>
                    </TextBlock>
                    <Border Background="#FAFAFA" 
                            BorderBrush="#EBEEF5"
                            BorderThickness="1" 
                            CornerRadius="4" 
                            Padding="15">
                        <TextBlock FontFamily="Consolas" 
                                   TextWrapping="Wrap">
                            <Run Text="&lt;hicon:HIcon IconName=&quot;home&quot; /&gt;"/>
                            <LineBreak/>
                            <Run Text="&lt;hicon:HIcon IconName=&quot;user&quot; IconSize=&quot;24&quot; IconColor=&quot;Red&quot; /&gt;"/>
                            <LineBreak/>
                            <Run Text="&lt;hicon:HIcon IconName=&quot;check&quot; IconColor=&quot;#67C23A&quot; /&gt;"/>
                        </TextBlock>
                    </Border>
                </StackPanel>
            </Border>
            
            <!-- 未来控件预留区域 -->
            <Border Background="White" 
                    BorderBrush="LightGray" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel>
                    <TextBlock Text="更多控件即将推出..." 
                               FontSize="18" 
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center"
                               Foreground="Gray"/>
                    <TextBlock Text="我们正在开发更多实用的控件，敬请期待！" 
                               HorizontalAlignment="Center"
                               Foreground="Gray"
                               Margin="0,10,0,0"/>
                </StackPanel>
            </Border>
            
        </StackPanel>
    </ScrollViewer>
</UserControl>
