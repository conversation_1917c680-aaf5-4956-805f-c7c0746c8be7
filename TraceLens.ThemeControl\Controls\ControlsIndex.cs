﻿using System;
using System.Collections.Generic;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// TraceLens 控件库索引
    /// 提供所有可用控件的信息和元数据
    /// </summary>
    public static class ControlsIndex
    {
        /// <summary>
        /// 控件信息
        /// </summary>
        public class ControlInfo
        {
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Version { get; set; } = string.Empty;
            public string Author { get; set; } = string.Empty;
            public DateTime CreatedDate { get; set; }
            public string FolderPath { get; set; } = string.Empty;
            public Type? ControlType { get; set; }
            public List<string> Features { get; set; } = new List<string>();
            public List<string> Dependencies { get; set; } = new List<string>();
        }

        /// <summary>
        /// 获取所有可用控件的信息
        /// </summary>
        /// <returns>控件信息列表</returns>
        public static List<ControlInfo> GetAllControls()
        {
            return new List<ControlInfo>
            {
                new ControlInfo
                {
                    Name = "HIcon",
                    Description = "基于 FontAwesome 4.7.0 的图标控件，支持通过英文名称直接使用图标",
                    Version = "1.0.0",
                    Author = "TraceLens Team",
                    CreatedDate = new DateTime(2025, 7, 31),
                    FolderPath = "Controls/HIcon",
                    ControlType = typeof(HIcon.HIcon),
                    Features = new List<string>
                    {
                        "支持 600+ FontAwesome 4.7.0 图标",
                        "Element UI 风格样式",
                        "多种预定义大小（Small, Medium, Large, ExtraLarge）",
                        "多种预定义颜色（Primary, Success, Warning, Danger, Info）",
                        "支持自定义大小和颜色",
                        "简单易用的 API",
                        "完整的使用示例"
                    },
                    Dependencies = new List<string>
                    {
                        "FontAwesome 4.7.0 字体文件",
                        "Element UI 颜色体系"
                    }
                }
                // 在这里添加更多控件信息...
            };
        }

        /// <summary>
        /// 根据名称获取控件信息
        /// </summary>
        /// <param name="controlName">控件名称</param>
        /// <returns>控件信息，如果未找到则返回 null</returns>
        public static ControlInfo? GetControlInfo(string controlName)
        {
            var controls = GetAllControls();
            return controls.Find(c => c.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取控件总数
        /// </summary>
        /// <returns>控件总数</returns>
        public static int GetControlCount()
        {
            return GetAllControls().Count;
        }

        /// <summary>
        /// 获取所有控件名称
        /// </summary>
        /// <returns>控件名称列表</returns>
        public static List<string> GetControlNames()
        {
            var controls = GetAllControls();
            return controls.ConvertAll(c => c.Name);
        }

        /// <summary>
        /// 检查控件是否存在
        /// </summary>
        /// <param name="controlName">控件名称</param>
        /// <returns>如果控件存在则返回 true</returns>
        public static bool ControlExists(string controlName)
        {
            return GetControlInfo(controlName) != null;
        }

        /// <summary>
        /// 获取控件库版本信息
        /// </summary>
        /// <returns>版本信息</returns>
        public static string GetLibraryVersion()
        {
            return "1.0.0";
        }

        /// <summary>
        /// 获取控件库描述
        /// </summary>
        /// <returns>库描述</returns>
        public static string GetLibraryDescription()
        {
            return "TraceLens 自定义控件库 - 基于 Element UI 设计风格的 WPF 控件集合";
        }
    }
}
